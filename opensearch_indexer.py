from elasticsearch import Elasticsearch
import mysql.connector
import json

# MySQL Config
MYSQL_CONFIG = {
    'host': 'rds-vuetra-staging.chm4q8ayi2a9.eu-central-1.rds.amazonaws.com',
    'port': 3306,
    'user': 'vuetra',
    'password': 'WK*CQzun5<4fgT$q8MH'
}

# Elasticsearch Config
ELASTICSEARCH_CLIENT = Elasticsearch(
    hosts=[{'host': 'localhost', 'port': 9200}],
    http_compress=True
)

client = Elasticsearch(
    hosts=[{'host': 'localhost', 'port': 9200}]
)

print(client.info())

def get_all_databases():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute("SHOW DATABASES")
    databases = [db[0] for db in cursor.fetchall()]
    cursor.close()
    conn.close()
    return databases

def get_tables(db):
    conn = mysql.connector.connect(database=db, **MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute("SHOW TABLES")
    tables = [t[0] for t in cursor.fetchall()]
    cursor.close()
    conn.close()
    return tables

def index_table(db, table):
    conn = mysql.connector.connect(database=db, **MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    cursor.execute(f"SELECT * FROM `{table}`")
    rows = cursor.fetchall()

    for row in rows:
        doc_id = f"{db}_{table}_{row.get('id', hash(json.dumps(row)))}"
        ELASTICSEARCH_CLIENT.index(
            index="global_search",
            id=doc_id,
            document={
                "database": db,
                "table": table,
                "data": row
            }
        )
    cursor.close()
    conn.close()

def index_all():
    databases = get_all_databases()
    for db in databases:
        if db in ['information_schema', 'mysql', 'performance_schema', 'sys']:
            continue
        tables = get_tables(db)
        for table in tables:
            index_table(db, table)
