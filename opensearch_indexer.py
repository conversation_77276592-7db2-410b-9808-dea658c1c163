from elasticsearch import Elasticsearch
import mysql.connector
import json

# MySQL Config - Using your actual RDS database
MYSQL_CONFIG = {
    'host': 'rds-vuetra-staging.chm4q8ayi2a9.eu-central-1.rds.amazonaws.com',
    'port': 3306,
    'user': 'vuetra',
    'password': 'WK*CQzun5<4fgT$q8MH',
    'database': 'vuetra_trading'
}

# Elasticsearch Config
ELASTICSEARCH_CLIENT = Elasticsearch(
    hosts=['http://localhost:9200']
)

client = Elasticsearch(
    hosts=['http://localhost:9200']
)

print(client.info())

def get_all_databases():
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute("SHOW DATABASES")
    databases = [db[0] for db in cursor.fetchall()]
    cursor.close()
    conn.close()
    return databases

def get_tables(db):
    conn = mysql.connector.connect(database=db, **MYSQL_CONFIG)
    cursor = conn.cursor()
    cursor.execute("SHOW TABLES")
    tables = [t[0] for t in cursor.fetchall()]
    cursor.close()
    conn.close()
    return tables

def index_table(db, table):
    conn = mysql.connector.connect(database=db, **MYSQL_CONFIG)
    cursor = conn.cursor(dictionary=True)
    cursor.execute(f"SELECT * FROM `{table}`")
    rows = cursor.fetchall()

    for row in rows:
        doc_id = f"{db}_{table}_{row.get('id', hash(json.dumps(row)))}"
        ELASTICSEARCH_CLIENT.index(
            index="global_search",
            id=doc_id,
            document={
                "database": db,
                "table": table,
                "data": row
            }
        )
    cursor.close()
    conn.close()

def index_all():
    # Index only the vuetra_trading database with your specific tables
    db = 'vuetra_trading'
    tables = ['economic_indicators', 'economic_indicators_categories', 'economic_indicators_history']

    print(f"Indexing database: {db}")
    for table in tables:
        print(f"  Indexing table: {table}")
        try:
            index_table(db, table)
            print(f"    ✓ Successfully indexed {table}")
        except Exception as e:
            print(f"    ✗ Error indexing {table}: {e}")

if __name__ == "__main__":
    print("Starting indexing process...")

    # Test database connection
    try:
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        cursor.execute('SHOW TABLES')
        tables = cursor.fetchall()
        print(f'Available tables in vuetra_trading: {tables}')
        cursor.close()
        conn.close()
        print("Database connection successful!")
    except Exception as e:
        print(f"Database connection failed: {e}")
        exit(1)

    # Test Elasticsearch connection
    try:
        es_info = client.info()
        print(f"Elasticsearch connection successful: {es_info['version']['number']}")
    except Exception as e:
        print(f"Elasticsearch connection failed: {e}")
        exit(1)

    # Start indexing
    index_all()
    print("Indexing completed!")
