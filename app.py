import streamlit as st
from elasticsearch import Elasticsearch
import json

# Connect to Elasticsearch
client = Elasticsearch(
    hosts=['http://localhost:9200']
)

st.set_page_config(
    page_title="Economic Indicators Search",
    page_icon="📊",
    layout="wide"
)

st.title("📊 Economic Indicators Smart Search")
st.write("Search across economic indicators data from vuetra_trading database")

# Sidebar for search options
st.sidebar.title("Search Options")
search_type = st.sidebar.radio(
    "Search Type",
    ["Basic Search", "Advanced Search"]
)

if search_type == "Basic Search":
    query = st.text_input("Enter search query (indicator name, country, value, etc.)")

    if query:
        body = {
            "query": {
                "multi_match": {
                    "query": query,
                    "fields": ["data.*"],
                    "fuzziness": "AUTO"
                }
            },
            "size": 50
        }

        try:
            res = client.search(index="global_search", body=body)
            hits = res['hits']['hits']

            st.write(f"Found {len(hits)} results")

            if hits:
                # Group results by table
                results_by_table = {}
                for hit in hits:
                    table = hit['_source']['table']
                    if table not in results_by_table:
                        results_by_table[table] = []
                    results_by_table[table].append(hit)

                # Display results in tabs by table
                tabs = st.tabs(list(results_by_table.keys()))

                for i, (table, table_hits) in enumerate(results_by_table.items()):
                    with tabs[i]:
                        st.write(f"**{len(table_hits)} results in {table}**")

                        # Create a more structured display based on the table
                        for hit in table_hits:
                            data = hit['_source']['data']

                            # Create a card-like display for each result
                            col1, col2 = st.columns([1, 2])

                            with col1:
                                if 'name' in data:
                                    st.subheader(data['name'])
                                elif 'indicator_name' in data:
                                    st.subheader(data['indicator_name'])
                                elif 'category_name' in data:
                                    st.subheader(data['category_name'])
                                else:
                                    st.subheader(f"ID: {data.get('id', 'Unknown')}")

                                if 'country' in data:
                                    st.write(f"**Country:** {data['country']}")

                                if 'date' in data:
                                    st.write(f"**Date:** {data['date']}")
                                elif 'timestamp' in data:
                                    st.write(f"**Date:** {data['timestamp']}")

                            with col2:
                                # Show all data in a formatted way
                                st.json(data)

                            st.markdown("---")
            else:
                st.warning("No results found")
        except Exception as e:
            st.error(f"Search error: {str(e)}")

else:  # Advanced Search
    col1, col2 = st.columns(2)

    with col1:
        indicator_name = st.text_input("Indicator Name")
        country = st.text_input("Country")

    with col2:
        category = st.text_input("Category")
        date_range = st.date_input("Date Range", value=[])

    if st.button("Search"):
        # Build advanced query
        must_conditions = []

        if indicator_name:
            must_conditions.append({
                "match": {"data.name": indicator_name}
            })

        if country:
            must_conditions.append({
                "match": {"data.country": country}
            })

        if category:
            must_conditions.append({
                "match": {"data.category": category}
            })

        if len(date_range) == 2:
            must_conditions.append({
                "range": {
                    "data.date": {
                        "gte": date_range[0].strftime("%Y-%m-%d"),
                        "lte": date_range[1].strftime("%Y-%m-%d")
                    }
                }
            })

        # If no conditions, show error
        if not must_conditions:
            st.warning("Please enter at least one search criterion")
        else:
            body = {
                "query": {
                    "bool": {
                        "must": must_conditions
                    }
                },
                "size": 50
            }

            try:
                res = client.search(index="global_search", body=body)
                hits = res['hits']['hits']

                st.write(f"Found {len(hits)} results")

                if hits:
                    for hit in hits:
                        st.write("---")
                        st.write(f"**Table:** {hit['_source']['table']}")
                        st.json(hit['_source']['data'])
                else:
                    st.warning("No results found")
            except Exception as e:
                st.error(f"Search error: {str(e)}")

# Footer
st.markdown("---")
st.markdown("**Smart Search System** - Powered by Elasticsearch and Streamlit")
