import streamlit as st
from elasticsearch import Elasticsearch

# Connect to Elasticsearch
client = Elasticsearch(
    hosts=[{'host': 'localhost', 'port': 9200}]
)

st.title("🔍 Global Smart Search")

query = st.text_input("Enter search term:")

if query:
    body = {
        "query": {
            "multi_match": {
                "query": query,
                "fields": ["data.*"]
            }
        }
    }
    res = client.search(index="global_search", body=body)
    hits = res['hits']['hits']

    if hits:
        st.subheader("Results:")
        for hit in hits:
            st.markdown(f"**Database:** `{hit['_source']['database']}`")
            st.markdown(f"**Table:** `{hit['_source']['table']}`")
            st.json(hit['_source']['data'])
    else:
        st.warning("No results found.")
